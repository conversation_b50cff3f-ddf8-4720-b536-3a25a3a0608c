"""
情感支持策略实现

处理用户的情感表达和支持需求，包括：
- 负面情绪安慰（沮丧、愤怒、焦虑等）
- 积极情绪回应（开心、兴奋、满意等）
- 情感倾诉支持（压力、困扰、疑虑等）

优先级: 5 (中低)
支持意图: emotional_support
"""

import logging
from typing import List, Dict, Any
import random

from ..decision_types import DecisionStrategy, AnalyzedContext, DecisionResult, create_decision_result
from ..unified_state_manager import ConversationState
from backend.config.unified_config_loader import get_unified_config


class EmotionalSupportStrategy(DecisionStrategy):
    """情感支持策略实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 情感关键词分类
        self.emotion_keywords = {
            "negative": {
                "anger": ["气死", "烦死", "愤怒", "生气", "不爽", "讨厌", "恶心"],
                "sadness": ["难过", "沮丧", "悲伤", "郁闷", "不开心", "痛苦", "绝望"],
                "anxiety": ["担心", "紧张", "害怕", "焦虑", "不安", "忧虑", "慌张"],
                "frustration": ["烦躁", "抓狂", "崩溃", "受不了", "要疯了", "倒霉"]
            },
            "positive": {
                "happiness": ["开心", "高兴", "兴奋", "激动", "愉快", "快乐", "满足"],
                "satisfaction": ["满意", "棒", "好的", "太好了", "完美", "优秀", "赞"],
                "gratitude": ["感谢", "谢谢", "感激", "感恩", "多谢", "谢了"],
                "excitement": ["激动", "兴奋", "期待", "迫不及待", "太棒了"]
            },
            "confused": ["不懂", "不明白", "搞不清", "糊涂", "迷茫", "困惑", "不知道"],
            "tired": ["累", "疲惫", "疲劳", "困", "没精神", "无力", "筋疲力尽"]
        }
        
        # 情感回应模板
        self.response_templates = {
            "negative": {
                "anger": [
                    get_unified_config().get_config_value("message_templates.emotional_support_strategy.anger_response_1"),
                    "感受到您的不满了。有时候遇到困难确实会让人生气，我会尽力帮您找到解决方案。",
                    "您的感受完全可以理解。让我们冷静下来，一步步分析问题所在。"
                ],
                "sadness": [
                    "我能感受到您现在的难过。虽然我不能完全理解您的感受，但我会陪伴您度过这个困难时期。",
                    "看到您这样难过，我也很心疼。请相信，困难总会过去的，我会尽我所能帮助您。",
                    "每个人都会有低落的时候，这很正常。让我们一起想想有什么可以让您感觉好一些的。"
                ],
                "anxiety": [
                    get_unified_config().get_config_value("message_templates.emotional_support_strategy.anxiety_response_1"),
                    "感受到您的不安了。有时候未知的事情确实会让人紧张，我们可以一步步来处理。",
                    "您的担心我能理解。让我们把问题分解一下，这样会更容易应对。"
                ],
                "frustration": [
                    "我能感受到您的挫败感。遇到困难时感到烦躁是很自然的，让我来帮您理清思路。",
                    "看得出您现在很烦躁。有时候事情确实会让人抓狂，我们一起想办法解决。",
                    "您的感受我完全理解。让我们暂停一下，重新整理思路。"
                ]
            },
            "positive": {
                "happiness": [
                    "太好了！看到您这么开心，我也很高兴。请分享一下是什么让您如此愉快？",
                    "您的好心情感染了我！有什么开心的事情想要分享吗？",
                    "真为您感到高兴！保持这种积极的心态，一切都会更顺利的。"
                ],
                "satisfaction": [
                    "很高兴您对结果满意！您的认可是对我最大的鼓励。",
                    "太棒了！看到您满意的样子，我也很有成就感。",
                    "您的满意就是我的目标！有其他需要帮助的地方吗？"
                ],
                "gratitude": [
                    "不用客气！能够帮助您是我的荣幸。有任何需要随时找我。",
                    "您的感谢让我很温暖！这就是我存在的意义，为您提供帮助。",
                    "谢谢您的认可！我会继续努力为您提供更好的服务。"
                ]
            },
            "confused": [
                get_unified_config().get_config_value("message_templates.emotional_support_strategy.confused_response_1"),
                "没关系，有不明白的地方很正常。我会用更简单的方式为您说明。",
                "您的困惑我能理解。让我换个角度来解释，看看是否更清楚。"
            ],
            "tired": [
                "看得出您很疲惫。要不要先休息一下？我随时在这里等您。",
                "感受到您的疲劳了。累的时候效率会下降，适当休息很重要。",
                "您辛苦了！如果现在不方便处理，我们可以稍后再继续。"
            ]
        }
        
        # 积极引导语句
        self.positive_guidance = [
            "不过，我相信我们一定能找到解决办法的。",
            "让我们把注意力转向解决方案上。",
            "虽然现在有些困难，但我会全力协助您。",
            "每个问题都有解决的方法，我们一起努力。",
            "相信自己，您比想象中更坚强。"
        ]
        
        # 情感识别强度
        self.emotion_intensity = {
            "mild": ["有点", "稍微", "略微", "一点"],
            "moderate": ["比较", "相当", "挺", "还是"],
            "strong": ["很", "非常", "特别", "极其", "超级", "太"]
        }
    
    @property
    def name(self) -> str:
        return "emotional_support_strategy"
    
    @property
    def supported_intents(self) -> List[str]:
        return ["emotional_support"]
    
    @property
    def priority(self) -> int:
        return 5  # 中低优先级
    
    async def can_handle(self, context: AnalyzedContext) -> bool:
        """
        判断是否能处理当前上下文
        
        Args:
            context: 分析后的上下文
            
        Returns:
            bool: 是否能处理
        """
        # 1. 基于意图判断
        if context.intent == "emotional_support":
            return True
        
        # 2. 基于情感分析结果判断
        if context.emotion in ["negative", "anxious", "confused", "positive"]:
            return True
        
        # 3. 基于情感关键词判断
        message_lower = context.message.lower()
        for emotion_category in self.emotion_keywords.values():
            if isinstance(emotion_category, dict):
                for emotion_type, keywords in emotion_category.items():
                    if any(keyword in message_lower for keyword in keywords):
                        return True
            elif isinstance(emotion_category, list):
                if any(keyword in message_lower for keyword in emotion_category):
                    return True
        
        # 4. 基于情感表达模式判断
        emotional_patterns = [
            r"我.*心情.*", r"感觉.*", r"觉得.*",
            r"让我.*", r"使我.*", r"令我.*"
        ]
        
        import re
        for pattern in emotional_patterns:
            if re.search(pattern, context.message):
                return True
        
        return False
    
    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        """
        执行情感支持策略
        
        Args:
            context: 分析后的上下文
            
        Returns:
            DecisionResult: 决策结果
        """
        try:
            # 分析情感类型和强度
            emotion_analysis = self._analyze_emotion_details(context)
            
            # 选择合适的回应策略
            response_strategy = self._select_response_strategy(emotion_analysis)
            
            # 生成情感支持回复
            response_content = self._generate_emotional_response(emotion_analysis, response_strategy)
            
            # 计算置信度
            confidence = await self.calculate_confidence(context)
            
            # 确定下一步动作
            action = self._determine_action(emotion_analysis)
            
            # 创建决策结果
            result = create_decision_result(
                action=action,
                confidence=confidence,
                intent=context.intent,
                emotion=context.emotion,
                response_template=response_content,
                next_state=ConversationState.IDLE,
                strategy_name=self.name,
                metadata={
                    "emotion_analysis": emotion_analysis,
                    "response_strategy": response_strategy,
                    "support_level": self._assess_support_level(emotion_analysis),
                    "requires_followup": self._requires_followup(emotion_analysis)
                }
            )
            
            self.logger.info(f"情感支持策略执行成功: emotion={emotion_analysis['primary_emotion']}, intensity={emotion_analysis['intensity']}")
            return result
            
        except Exception as e:
            self.logger.error(f"情感支持策略执行失败: {e}", exc_info=True)
            
            # 返回基础情感支持
            return create_decision_result(
                action="provide_emotional_support",
                confidence=0.7,
                intent="emotional_support",
                emotion="neutral",
                response_template=get_unified_config().get_config_value("message_templates.emotional_support_strategy.fallback_response"),
                next_state=ConversationState.IDLE,
                strategy_name=self.name,
                metadata={"error": str(e), "fallback": True}
            )
    
    async def calculate_confidence(self, context: AnalyzedContext) -> float:
        """
        计算处理置信度
        
        Args:
            context: 分析后的上下文
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        confidence_factors = []
        
        # 1. 意图匹配度
        if context.intent == "emotional_support":
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.4)
        
        # 2. 情感分析结果
        if context.emotion in ["negative", "anxious", "positive"]:
            confidence_factors.append(0.8)
        elif context.emotion == "confused":
            confidence_factors.append(0.6)
        else:
            confidence_factors.append(0.3)
        
        # 3. 情感关键词匹配度
        emotion_score = self._calculate_emotion_keyword_score(context)
        confidence_factors.append(emotion_score)
        
        # 4. 情感表达强度
        intensity = self._detect_emotion_intensity(context.message)
        if intensity == "strong":
            confidence_factors.append(0.9)
        elif intensity == "moderate":
            confidence_factors.append(0.7)
        else:
            confidence_factors.append(0.5)
        
        return sum(confidence_factors) / len(confidence_factors)
    
    def _analyze_emotion_details(self, context: AnalyzedContext) -> Dict[str, Any]:
        """分析情感详细信息"""
        message_lower = context.message.lower()
        
        # 检测主要情感类型
        primary_emotion = "neutral"
        emotion_keywords_found = []
        
        for emotion_category, emotions in self.emotion_keywords.items():
            if isinstance(emotions, dict):
                for emotion_type, keywords in emotions.items():
                    for keyword in keywords:
                        if keyword in message_lower:
                            primary_emotion = emotion_type
                            emotion_keywords_found.append(keyword)
            elif isinstance(emotions, list):
                for keyword in emotions:
                    if keyword in message_lower:
                        primary_emotion = emotion_category
                        emotion_keywords_found.append(keyword)
        
        # 检测情感强度
        intensity = self._detect_emotion_intensity(context.message)
        
        # 检测情感原因
        cause = self._detect_emotion_cause(context.message)
        
        return {
            "primary_emotion": primary_emotion,
            "keywords_found": emotion_keywords_found,
            "intensity": intensity,
            "cause": cause,
            "context_emotion": context.emotion
        }
    
    def _detect_emotion_intensity(self, message: str) -> str:
        """检测情感强度"""
        for intensity, indicators in self.emotion_intensity.items():
            if any(indicator in message for indicator in indicators):
                return intensity
        return "mild"
    
    def _detect_emotion_cause(self, message: str) -> str:
        """检测情感原因"""
        cause_patterns = {
            "work": ["工作", "上班", "同事", "老板", "项目", "任务"],
            "personal": ["家人", "朋友", "恋人", "家庭", "关系"],
            "health": ["身体", "健康", "生病", "疼痛", "不舒服"],
            "technical": ["系统", "软件", "程序", "bug", "错误", "故障"],
            "general": ["问题", "困难", "麻烦", "事情"]
        }
        
        message_lower = message.lower()
        for cause_type, keywords in cause_patterns.items():
            if any(keyword in message_lower for keyword in keywords):
                return cause_type
        
        return "unknown"
    
    def _select_response_strategy(self, emotion_analysis: Dict[str, Any]) -> str:
        """选择回应策略"""
        primary_emotion = emotion_analysis["primary_emotion"]
        intensity = emotion_analysis["intensity"]
        
        if primary_emotion in ["anger", "sadness", "anxiety", "frustration"]:
            if intensity == "strong":
                return "deep_empathy"
            else:
                return "gentle_support"
        elif primary_emotion in ["happiness", "satisfaction", "gratitude"]:
            return "positive_reinforcement"
        elif primary_emotion == "confused":
            return "clarification_support"
        else:
            return "general_support"
    
    def _generate_emotional_response(self, emotion_analysis: Dict[str, Any], strategy: str) -> str:
        """生成情感支持回复"""
        primary_emotion = emotion_analysis["primary_emotion"]
        
        # 选择基础回应模板
        if primary_emotion in self.response_templates["negative"]:
            base_response = random.choice(self.response_templates["negative"][primary_emotion])
        elif primary_emotion in self.response_templates["positive"]:
            base_response = random.choice(self.response_templates["positive"][primary_emotion])
        elif primary_emotion in self.response_templates:
            base_response = random.choice(self.response_templates[primary_emotion])
        else:
            base_response = get_unified_config().get_config_value("message_templates.emotional_support_strategy.default_understanding")
        
        # 根据策略添加引导语句
        if strategy in ["deep_empathy", "gentle_support"] and primary_emotion in ["anger", "sadness", "anxiety", "frustration"]:
            guidance = random.choice(self.positive_guidance)
            return f"{base_response} {guidance}"
        
        return base_response
    
    def _determine_action(self, emotion_analysis: Dict[str, Any]) -> str:
        """确定处理动作"""
        primary_emotion = emotion_analysis["primary_emotion"]
        intensity = emotion_analysis["intensity"]
        
        if primary_emotion in ["anger", "frustration"] and intensity == "strong":
            return "provide_calming_support"
        elif primary_emotion in ["sadness", "anxiety"]:
            return "provide_emotional_comfort"
        elif primary_emotion in ["happiness", "satisfaction"]:
            return "reinforce_positive_emotion"
        elif primary_emotion == "confused":
            return "provide_clarification_support"
        else:
            return "provide_emotional_support"
    
    def _calculate_emotion_keyword_score(self, context: AnalyzedContext) -> float:
        """计算情感关键词匹配分数"""
        message_lower = context.message.lower()
        total_matches = 0
        total_keywords = 0
        
        for emotion_category in self.emotion_keywords.values():
            if isinstance(emotion_category, dict):
                for keywords in emotion_category.values():
                    total_keywords += len(keywords)
                    total_matches += sum(1 for keyword in keywords if keyword in message_lower)
            elif isinstance(emotion_category, list):
                total_keywords += len(emotion_category)
                total_matches += sum(1 for keyword in emotion_category if keyword in message_lower)
        
        if total_keywords == 0:
            return 0.0
        
        return min(0.9, total_matches / total_keywords * 10)  # 放大匹配效果
    
    def _assess_support_level(self, emotion_analysis: Dict[str, Any]) -> str:
        """评估所需支持级别"""
        intensity = emotion_analysis["intensity"]
        primary_emotion = emotion_analysis["primary_emotion"]
        
        if intensity == "strong" and primary_emotion in ["anger", "sadness", "anxiety"]:
            return "high"
        elif intensity == "moderate":
            return "medium"
        else:
            return "low"
    
    def _requires_followup(self, emotion_analysis: Dict[str, Any]) -> bool:
        """判断是否需要后续跟进"""
        return (emotion_analysis["intensity"] == "strong" and 
                emotion_analysis["primary_emotion"] in ["anger", "sadness", "anxiety", "frustration"])
    
    async def calculate_context_match(self, context: AnalyzedContext) -> float:
        """
        计算与上下文的匹配度
        
        Args:
            context: 分析后的上下文
            
        Returns:
            float: 匹配度 (0.0-1.0)
        """
        if context.intent in self.supported_intents:
            return 1.0
        
        # 基于情感分析和关键词的匹配度
        emotion_score = 0.0
        if context.emotion in ["negative", "anxious", "positive"]:
            emotion_score = 0.7
        elif context.emotion == "confused":
            emotion_score = 0.5
        
        keyword_score = self._calculate_emotion_keyword_score(context)
        
        return min(1.0, emotion_score + keyword_score)


# 导出策略类
__all__ = ['EmotionalSupportStrategy']
