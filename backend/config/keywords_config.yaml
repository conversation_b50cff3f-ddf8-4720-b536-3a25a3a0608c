# 统一关键词配置文件
# 这是所有知识库查询关键词的单一数据源
# 版本: 1.0
# 创建时间: 2025-08-08
# 说明: 整合了之前分散在8个不同位置的关键词定义
#
# 🔧 [关键词重构] 这是新的统一关键词配置系统
# 替代了以下旧配置中的重复关键词定义:
# - unified_config.yaml 中的 keyword_acceleration 和 keyword_rules
# - intent_definitions.yaml 中的示例关键词
# - 各个模块中的硬编码关键词列表

# 知识库查询关键词配置
knowledge_base_keywords:
  # 价格相关查询
  pricing:
    - "价格"
    - "费用"
    - "收费"
    - "套餐"
    - "计费"
    - "多少钱"
    - "收费标准"
    - "价格表"
    - "价格说明"
    - "付费"
    - "免费"
    - "成本"
    - "报价"
    - "预算"
    - "花费"
    - "开销"

  # 功能相关查询
  features:
    - "功能"
    - "特点"
    - "能力"
    - "支持"
    - "提供"
    - "包含"
    - "有什么"
    - "能做什么"
    - "功能列表"
    - "服务内容"
    - "特色"
    - "优势"
    - "亮点"

  # 使用方法查询
  usage:
    - "怎么用"
    - "如何使用"
    - "使用方法"
    - "操作步骤"
    - "使用说明"
    - "教程"
    - "指南"
    - "帮助"
    - "说明书"
    - "手册"

  # 注册相关查询
  registration:
    - "注册"
    - "开户"
    - "申请"
    - "账号"
    - "登录"
    - "注册流程"
    - "开通"
    - "激活"

  # 技术支持查询
  support:
    - "技术支持"
    - "客服"
    - "联系方式"
    - "售后"
    - "问题"
    - "故障"
    - "bug"
    - "错误"
    - "异常"

  # 产品信息查询
  product_info:
    - "产品介绍"
    - "产品说明"
    - "详细信息"
    - "规格"
    - "参数"
    - "配置"
    - "版本"
    - "更新"

# 系统能力查询关键词（用于关键词加速）
system_capability_keywords:
  general_inquiry:
    - "你能做什么"
    - "你会什么"
    - "你的功能"
    - "有什么功能"
    - "能帮我什么"
    - "你是什么"
    - "介绍一下"
    - "你好"
    - "hello"
    - "hi"

  greeting:
    - "你好"
    - "您好"
    - "hello"
    - "hi"
    - "早上好"
    - "下午好"
    - "晚上好"

# 意图识别关键词映射
intent_keywords:
  # 知识库查询意图的所有关键词（合并所有分类）
  search_knowledge_base:
    - "价格"
    - "费用"
    - "收费"
    - "套餐"
    - "计费"
    - "多少钱"
    - "收费标准"
    - "价格表"
    - "价格说明"
    - "功能"
    - "特点"
    - "能力"
    - "支持"
    - "有什么"
    - "能做什么"
    - "怎么用"
    - "如何使用"
    - "使用方法"
    - "注册"
    - "开户"
    - "申请"
    - "技术支持"
    - "客服"
    - "联系方式"
    - "产品介绍"
    - "产品说明"
    - "如何注册"
    - "怎么注册"
    - "注册流程"
    - "账号注册"
    - "操作步骤"
    - "什么是"
    - "介绍一下"
    - "功能说明"
    - "如何操作"
    - "支持哪些"
    - "有哪些功能"
    - "提供什么"
    - "能用什么"
    - "登录方法"
    - "密码重置"
    - "忘记密码"
    - "服务介绍"
    - "帮助文档"
    - "常见问题"
    - "FAQ"
    - "问题解答"
    - "疑难解答"

  # 问候关键词
  greeting:
    - "你好"
    - "您好"
    - "hi"
    - "hello"
    - "嗨"
    - "早上好"
    - "下午好"
    - "晚上好"

  # 自我介绍关键词
  ask_introduction:
    - "自我介绍"
    - "介绍自己"
    - "你是谁"
    - "介绍一下你自己"

  # 能力询问关键词
  ask_capabilities:
    - "你能做什么"
    - "有什么功能"
    - "能力"
    - "功能介绍"
    - "你会什么"
    - "你的功能"
    - "能帮我什么"
    - "你是什么"

  # 业务需求关键词
  business_requirement:
    - "我想"
    - "我需要"
    - "帮我"
    - "制作"
    - "设计"
    - "开发"
    - "创建"
    - "海报"
    - "网站"
    - "app"
    - "应用"
    - "系统"
    - "平台"
    - "项目"

  # 情感支持关键词
  emotional_support:
    - "心情不好"
    - "安慰我"
    - "难过"
    - "沮丧"
    - "不开心"
    - "郁闷"
    - "气死我了"
    - "气死了"
    - "烦死了"
    - "烦死我了"
    - "受不了"
    - "要疯了"
    - "崩溃"
    - "抓狂"
    - "痛苦"
    - "悲伤"
    - "愤怒"
    - "生气"
    - "不爽"
    - "烦躁"

  # 一般聊天关键词
  general_chat:
    - "聊天"
    - "闲聊"
    - "随便聊聊"
    - "说说话"
    - "陪我聊天"

# 情感分析关键词配置
emotion_keywords:
  # 负面情感
  negative:
    - "气死"
    - "烦死"
    - "郁闷"
    - "沮丧"
    - "难过"
    - "不开心"
    - "讨厌"
    - "恶心"
    - "糟糕"
    - "倒霉"
    - "失望"
    - "绝望"
    - "痛苦"
    - "悲伤"
    - "愤怒"
    - "生气"
    - "不爽"
    - "烦躁"
    - "抓狂"
    - "崩溃"
    - "受不了"
    - "要疯了"

  # 焦虑情感
  anxious:
    - "担心"
    - "紧张"
    - "害怕"
    - "恐惧"
    - "不安"
    - "焦虑"
    - "忧虑"
    - "慌张"
    - "着急"
    - "急死"
    - "怎么办"
    - "完了"
    - "糟了"
    - "坏了"
    - "紧急"

  # 困惑情感
  confused:
    - "不懂"
    - "不明白"
    - "搞不清"
    - "糊涂"
    - "迷茫"
    - "困惑"
    - "不知道"
    - "怎么回事"
    - "什么意思"
    - "看不懂"
    - "理解不了"
    - "搞不懂"

  # 积极情感
  positive:
    - "开心"
    - "高兴"
    - "兴奋"
    - "激动"
    - "满意"
    - "棒"
    - "好的"
    - "太好了"
    - "完美"
    - "优秀"
    - "赞"
    - "喜欢"
    - "爱"
    - "感谢"
    - "谢谢"

# 配置元数据
metadata:
  version: "1.1"
  created_date: "2025-08-08"
  updated_date: "2025-08-10"
  description: "统一关键词配置，整合了之前分散的关键词定义，包括意图识别和情感分析关键词"
  migration_sources:
    - "backend/agents/strategies/knowledge_base_strategy.py"
    - "backend/agents/simplified_decision_engine.py"
    - "backend/agents/context_analyzer.py"
    - "backend/handlers/requirement_handler.py"
    - "backend/config/intent_definitions.yaml"
    - "backend/config/unified_config.yaml"
  latest_changes:
    - "添加了完整的意图识别关键词配置"
    - "添加了情感分析关键词配置"
    - "为context_analyzer.py的配置化改造做准备"
  
# 向后兼容性配置
compatibility:
  # 保持与现有代码的兼容性
  legacy_mapping:
    # knowledge_base_strategy.py 的分类映射
    pricing: "pricing"
    features: "features"
    usage: "usage"
    registration: "registration"
    support: "support"
    product_info: "product_info"
